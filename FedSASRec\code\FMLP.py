import time
import math
import copy
from copy import deepcopy
from tqdm import tqdm

import torch
import torch.nn as nn
import torch.nn.functional as F

class GeLu(nn.Module):
    def __init__(self):
        super(GeLu, self).__init__()

    def forward(self, x):
        return x * 0.5 * (1.0 + torch.erf(x / math.sqrt(2.0)))
    
class Swish(nn.Module):
    def __init__(self):
        super(Swish, self).__init__()

    def forward(self, x):
        return x * torch.sigmoid(x)

ACT2FN = {"gelu": GeLu(), "relu": nn.ReLU(), "swish": Swish()}

class LayerNorm(nn.Module):
    def __init__(self, hidden_size, eps=1e-12):
        super(LayerNorm, self).__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.bias = nn.Parameter(torch.zeros(hidden_size))
        self.variance_epsilon = eps

    def forward(self, x):
        u = x.mean(-1, keepdim=True)
        s = (x - u).pow(2).mean(-1, keepdim=True)
        x = (x - u) / torch.sqrt(s + self.variance_epsilon)
        return self.weight * x + self.bias
    
class FilterLayer(nn.Module):
    def __init__(self, max_seq_length, hidden_size, hidden_dropout_prob=0.5):
        super(FilterLayer, self).__init__()
        self.complex_weight = nn.Parameter(
            torch.randn(1, max_seq_length // 2 + 1, hidden_size, 2, dtype=torch.float32) * 0.02
        )
        self.out_dropout = nn.Dropout(hidden_dropout_prob)
        self.layernorm = LayerNorm(hidden_size, eps=1e-12)

    def forward(self, input_tensor):
        # [batch, seq_len, hidden]
        batch, seq_len, hidden = input_tensor.shape
        x = torch.fft.rfft(input_tensor, dim=1, norm='ortho')
        weight = torch.view_as_complex(self.complex_weight)
        x = x * weight
        sequence_emb_fft = torch.fft.irfft(x, n=seq_len, dim=1, norm='ortho')
        hidden_states = self.out_dropout(sequence_emb_fft)
        hidden_states = self.layernorm(hidden_states + input_tensor)

        return hidden_states

class Intermediate(nn.Module):
    def __init__(self, hidden_size, hidden_act='gelu', hidden_dropout_prob=0.5):
        super(Intermediate, self).__init__()
        self.dense_1 = nn.Linear(hidden_size, hidden_size * 4)
        assert isinstance(hidden_act, str), 'invalid hidden acivation'
        self.intermediate_act_fn = ACT2FN[hidden_act]

        self.dense_2 = nn.Linear(4 * hidden_size, hidden_size)
        self.layernorm = LayerNorm(hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(hidden_dropout_prob)

    def forward(self, input_tensor):
        hidden_states = self.dense_1(input_tensor)
        hidden_states = self.intermediate_act_fn(hidden_states)

        hidden_states = self.dense_2(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.layernorm(hidden_states + input_tensor)

        return hidden_states

class Layer(nn.Module):
    def __init__(self, max_seq_length, hidden_size, hidden_dropout_prob, hidden_act):
        super(Layer, self).__init__()
        self.filterlayer = FilterLayer(max_seq_length, hidden_size, hidden_dropout_prob)
        self.intermediate = Intermediate(hidden_size, hidden_act, hidden_dropout_prob)

    def forward(self, hidden_states):
        hidden_states = self.filterlayer(hidden_states)
        intermediate_output = self.intermediate(hidden_states)

        return intermediate_output

class Encoder(nn.Module):
    def __init__(self, n_layers, max_seq_length, hidden_size, hidden_dropout_prob, hidden_act):
        super(Encoder, self).__init__()
        layer = Layer(max_seq_length, hidden_size, hidden_dropout_prob, hidden_act)
        self.layer = nn.ModuleList([copy.deepcopy(layer)
                                    for _ in range(n_layers)])

    def forward(self, hidden_states, output_all_encoded_layers=True):
        all_encoder_layers = []
        for layer_module in self.layer:
            hidden_states = layer_module(hidden_states)
            if output_all_encoded_layers:
                all_encoder_layers.append(hidden_states)
        if not output_all_encoded_layers:
            all_encoder_layers.append(hidden_states)
        return all_encoder_layers

class FMLP(nn.Module):
    """
    FMLP模型类，适配联邦学习框架
    基于傅里叶变换的序列推荐模型，与SASRec接口兼容
    """
    def __init__(self, config, item_maxid):
        super(FMLP, self).__init__()
        
        # 从config中提取参数，保持与SASRec一致的接口
        self.n_layers = config.get('num_layers', 2)
        self.hidden_size = config.get('item_embedding_dim', 64)
        self.hidden_dropout_prob = config.get('dropout_prob', 0.5)
        self.max_seq_length = config.get('max_seq_len', 200)
        
        # 设备配置
        self.device = config.get('device', "cuda" if torch.cuda.is_available() else "cpu")
        
        # 物品数量（包括padding）
        self.n_items = item_maxid + 1
        
        # 嵌入层
        self.item_embedding = nn.Embedding(self.n_items, self.hidden_size, padding_idx=0)
        self.position_embedding = nn.Embedding(self.max_seq_length, self.hidden_size)

        # LayerNorm和Dropout
        self.layernorm = LayerNorm(self.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(self.hidden_dropout_prob)

        # FMLP编码器
        self.item_encoder = Encoder(
            n_layers=self.n_layers,
            max_seq_length=self.max_seq_length,
            hidden_size=self.hidden_size,
            hidden_dropout_prob=self.hidden_dropout_prob, 
            hidden_act='gelu',
        )

        # 初始化参数
        self.apply(self.init_weights)

    def init_weights(self, module):
        """初始化模型参数"""
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=0.02)
        elif isinstance(module, LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()

    def gather_indexes(self, output, gather_index):
        """收集指定位置的输出"""
        gather_index = gather_index.view(-1, 1, 1).expand(-1, -1, output.shape[-1])
        output_tensor = output.gather(dim=1, index=gather_index)
        return output_tensor.squeeze(1)

    def add_position_embedding(self, sequence):
        """添加位置嵌入"""
        seq_length = sequence.size(1) 
        position_ids = torch.arange(seq_length, dtype=torch.long, device=sequence.device)
        position_ids = position_ids.unsqueeze(0).expand_as(sequence)
        item_embeddings = self.item_embedding(sequence)
        position_embeddings = self.position_embedding(position_ids)
        sequence_emb = item_embeddings + position_embeddings
        sequence_emb = self.layernorm(sequence_emb)
        sequence_emb = self.dropout(sequence_emb)

        return sequence_emb
    
    def forward(self, x, x_lens):
        """
        前向传播，与SASRec接口兼容
        
        参数:
        - x: 输入的物品序列 (batch_size, seq_len)
        - x_lens: 每个序列的实际长度 (batch_size)
        
        返回:
        - seq_output: 模型对序列中每个位置的下一个物品的预测得分
        """
        item_seq = x
        
        # 确保序列长度不超过模型定义的最大长度
        if item_seq.size(1) > self.max_seq_length:
            item_seq = item_seq[:, -self.max_seq_length:]
            x_lens = torch.clamp(x_lens, max=self.max_seq_length)
        
        # 确保item_seq中的ID不超出范围
        item_seq = torch.clamp(item_seq, 0, self.n_items - 1)
        
        # 添加位置嵌入
        sequence_emb = self.add_position_embedding(item_seq)
        
        # FMLP编码
        item_encoded_layers = self.item_encoder(
            sequence_emb,
            output_all_encoded_layers=True)
        
        output = item_encoded_layers[-1]
        
        # 计算每个物品的预测得分
        seq_output = torch.matmul(output, self.item_embedding.weight.transpose(0, 1))

        return seq_output

    def loss_function(self, seq_out, padding_mask, target, neg, seq_len):
        """
        计算损失函数，与SASRec接口兼容
        使用交叉熵损失
        """
        # 提取目标物品(正样本)的预测分数
        target_output = torch.gather(seq_out, 2, target.unsqueeze(-1).long())

        # 使用二元交叉熵损失
        if neg.dim() == 3:
            # 多个负样本的情况
            neg_output = torch.gather(seq_out, 2, neg.long())
            pos_loss = -torch.log(torch.sigmoid(target_output))
            neg_loss = -torch.log(1 - torch.sigmoid(neg_output)).mean(dim=-1, keepdim=True)
        else:
            # 单个负样本的情况
            neg_output = torch.gather(seq_out, 2, neg.unsqueeze(-1).long())
            pos_loss = -torch.log(torch.sigmoid(target_output))
            neg_loss = -torch.log(1 - torch.sigmoid(neg_output))

        # 合并正负样本损失
        loss = pos_loss + neg_loss

        # 应用掩码，确保只计算非填充位置的损失
        loss = loss * padding_mask.unsqueeze(-1)

        # 计算平均损失
        non_zero_elements = padding_mask.sum()
        if non_zero_elements > 0:
            loss = loss.sum() / non_zero_elements
        else:
            loss = loss.sum()

        return loss

    def predict(self, user_ids, log_seqs, item_indices):
        """
        预测方法，与SASRec接口兼容
        """
        # 转换为tensor
        if not isinstance(log_seqs, torch.Tensor):
            log_seqs = torch.LongTensor(log_seqs).to(self.device)
        
        # 计算序列长度
        seq_lens = torch.sum(log_seqs != 0, dim=1)
        
        # 前向传播
        seq_output = self.forward(log_seqs, seq_lens)
        
        # 获取最后一个位置的特征
        final_feat = []
        for i in range(log_seqs.shape[0]):
            last_idx = min(log_seqs.shape[1] - 1, seq_lens[i].item() - 1)
            final_feat.append(seq_output[i, last_idx, :])
        
        final_feat = torch.stack(final_feat)
        
        # 转换为tensor
        if not isinstance(item_indices, torch.Tensor):
            item_indices = torch.LongTensor(item_indices).to(self.device)

        item_embs = self.item_embedding(item_indices)
        print(item_embs.shape)
        print (final_feat.shape)
        logits = item_embs.matmul(final_feat.unsqueeze(-1)).squeeze(-1)

        return logits
