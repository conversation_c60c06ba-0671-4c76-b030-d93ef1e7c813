"""
模型工厂函数
根据配置参数选择并初始化相应的模型（SASRec或FMLP）
确保接口统一，便于联邦学习框架使用
"""

import torch
from SASRec import SASRec
from FMLP import FMLP


def create_model(config, item_maxid):
    """
    模型工厂函数，根据配置创建相应的模型
    
    参数:
    - config: 配置参数字典，包含模型类型和相关参数
    - item_maxid: 最大物品ID
    
    返回:
    - model: 初始化好的模型实例
    """
    model_type = config.get('model', 'SASRec').upper()
    
    if model_type == 'SASREC':
        # 创建SASRec模型
        model = SASRec(config, item_maxid)
        
    elif model_type == 'FMLP':
        # 创建FMLP模型
        model = FMLP(config, item_maxid)
        
    else:
        raise ValueError(f"不支持的模型类型: {model_type}. 支持的模型: ['SASRec', 'FMLP']")
    
    return model


def get_model_info(config):
    """
    获取模型信息，用于日志记录
    
    参数:
    - config: 配置参数字典
    
    返回:
    - info: 模型信息字典
    """
    model_type = config.get('model', 'SASRec').upper()
    
    if model_type == 'SASREC':
        info = {
            'model_name': 'SASRec',
            'description': '基于自注意力机制的序列推荐模型',
            'key_params': {
                'embed_dim': config.get('embed_dim', 50),
                'num_layers': config.get('num_layers', 2),
                'num_heads': config.get('num_heads', 1),
                'dropout': config.get('dropout', 0.2),
                'max_seq_len': config.get('max_seq_len', 200)
            }
        }
        
    elif model_type == 'FMLP':
        info = {
            'model_name': 'FMLP',
            'description': '基于傅里叶变换的序列推荐模型',
            'key_params': {
                'item_embedding_dim': config.get('item_embedding_dim', 64),
                'num_layers': config.get('num_layers', 2),
                'dropout_prob': config.get('dropout_prob', 0.5),
                'max_seq_len': config.get('max_seq_len', 200)
            }
        }
        
    else:
        info = {
            'model_name': 'Unknown',
            'description': f'未知模型类型: {model_type}',
            'key_params': {}
        }
    
    return info


def validate_config(config):
    """
    验证配置参数的有效性
    
    参数:
    - config: 配置参数字典
    
    返回:
    - is_valid: 配置是否有效
    - error_msg: 错误信息（如果有）
    """
    model_type = config.get('model', 'SASRec').upper()
    
    # 检查模型类型
    if model_type not in ['SASREC', 'FMLP']:
        return False, f"不支持的模型类型: {model_type}"
    
    # 检查通用参数
    required_params = ['max_seq_len', 'num_layers']
    for param in required_params:
        if param not in config:
            return False, f"缺少必需参数: {param}"
        if not isinstance(config[param], int) or config[param] <= 0:
            return False, f"参数 {param} 必须是正整数"
    
    # 检查模型特定参数
    if model_type == 'SASREC':
        sasrec_params = ['embed_dim', 'num_heads']
        for param in sasrec_params:
            if param not in config:
                return False, f"SASRec模型缺少必需参数: {param}"
            if not isinstance(config[param], int) or config[param] <= 0:
                return False, f"SASRec参数 {param} 必须是正整数"
                
        # 检查embed_dim是否能被num_heads整除
        if config['embed_dim'] % config['num_heads'] != 0:
            return False, "SASRec的embed_dim必须能被num_heads整除"
    
    elif model_type == 'FMLP':
        fmlp_params = ['item_embedding_dim']
        for param in fmlp_params:
            if param not in config:
                return False, f"FMLP模型缺少必需参数: {param}"
            if not isinstance(config[param], int) or config[param] <= 0:
                return False, f"FMLP参数 {param} 必须是正整数"
    
    return True, ""


def get_optimizer_config(config, model):
    """
    根据模型类型获取优化器配置
    
    参数:
    - config: 配置参数字典
    - model: 模型实例
    
    返回:
    - optimizer_config: 优化器配置字典
    """
    model_type = config.get('model', 'SASRec').upper()
    
    if model_type == 'SASREC':
        optimizer_config = {
            'lr': config.get('lr', 0.001),
            'betas': (0.9, 0.98),
            'weight_decay': config.get('l2_reg', 0.0)
        }
        
    elif model_type == 'FMLP':
        optimizer_config = {
            'lr': config.get('learning_rate', 0.001),
            'betas': (0.9, 0.999),  # FMLP使用标准的Adam参数
            'weight_decay': config.get('weight_decay', 0.0)
        }
        
    else:
        # 默认配置
        optimizer_config = {
            'lr': 0.001,
            'betas': (0.9, 0.999),
            'weight_decay': 0.0
        }
    
    return optimizer_config


def create_optimizer(config, model):
    """
    为模型创建优化器
    
    参数:
    - config: 配置参数字典
    - model: 模型实例
    
    返回:
    - optimizer: 优化器实例
    """
    optimizer_config = get_optimizer_config(config, model)
    
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=optimizer_config['lr'],
        betas=optimizer_config['betas'],
        weight_decay=optimizer_config['weight_decay']
    )
    
    return optimizer
