<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="97be9152-57e7-4144-aea6-2e83e8227c53" name="更改" comment="代码细节调整 负采样错误修正">
      <change afterPath="$PROJECT_DIR$/FedSASRec/code/refer/fmlp.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/Fed.py" beforeDir="false" afterPath="$PROJECT_DIR$/FedSASRec/code/Fed.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/old/7.21_incorrect_fed/Fed.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/old/7.21_incorrect_fed/dataset.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/parse.py" beforeDir="false" afterPath="$PROJECT_DIR$/FedSASRec/code/parse.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/refer/LLMS/Fed.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/refer/LLMS/dataset.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/refer/primx/main.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/refer/primx/model.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/FedSASRec/code/refer/primx/utils.py" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/data/ml-1m/test.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/data/ml-1m/train.csv" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/data/ml-1m/valid.csv" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Leaves-XY&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Leaves-XY/FedSeqRecCodePro.git&quot;,
    &quot;accountId&quot;: &quot;bde31650-06c5-4336-a14f-8a1cd0716eab&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/FedSASRec/log/ml-100k/important/动态采样+无layerNorm层优化_ml-100k.txt_len(200)_Jul212025152747.log" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/FedSASRec/log/ml-1m/important/原层归一化_动态采样_ml-1m.log" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/FedSASRec/log/ml-1m/important/原层归一化_静态采样_ml-1m_en(200).log" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/FedSASRec/log/ml-1m/important/新层归一化_静态采样_ml-1m_en(200).log" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30ArRvXmPB5neowK4zveaOXev8p" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.main.executor": "Run",
    "Python.test_norm_first.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "E:/Project/fedrec/fedseqrec/FedSeqRecCodePro/FedSASRec/code/refer",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Project\fedrec\fedseqrec\FedSeqRecCodePro\FedSASRec\code\refer" />
      <recent name="E:\Project\fedrec\fedseqrec\FedSeqRecCodePro\FedSASRec\log\ml-1m\important" />
      <recent name="E:\Project\fedrec\fedseqrec\FedSeqRecCodePro\FedSASRec\log\ml-100k\important" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Project\fedrec\fedseqrec\FedSeqRecCodePro\FedSASRec\code\refer" />
      <recent name="E:\Project\fedrec\fedseqrec\FedSeqRecCodePro\FedSASRec\log\ml-100k\important" />
    </key>
  </component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FedSeqRecCodePro" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/FedSASRec/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/FedSASRec/code/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_norm_first" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="FedSeqRecCodePro" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/FedSASRec/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/FedSASRec/code/test_norm_first.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.test_norm_first" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.74" />
        <option value="bundled-python-sdk-657d8234b839-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.74" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="97be9152-57e7-4144-aea6-2e83e8227c53" name="更改" comment="" />
      <created>1753081213836</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753081213836</updated>
      <workItem from="1753081215298" duration="3478000" />
      <workItem from="1753085756957" duration="7468000" />
      <workItem from="1753164371346" duration="24000" />
      <workItem from="1753172007401" duration="4827000" />
      <workItem from="1753232006528" duration="1368000" />
      <workItem from="1753255482235" duration="6117000" />
      <workItem from="1753340523996" duration="2043000" />
      <workItem from="1753409675563" duration="1839000" />
      <workItem from="1753415575895" duration="128000" />
      <workItem from="1753421558474" duration="2682000" />
      <workItem from="1753664353431" duration="568000" />
      <workItem from="1753665859782" duration="12382000" />
      <workItem from="1754012987221" duration="898000" />
      <workItem from="1754029057488" duration="3477000" />
    </task>
    <task id="LOCAL-00001" summary="继续融合SAS.torch 用pre-ln改进性能">
      <option name="closed" value="true" />
      <created>1753084821776</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753084821776</updated>
    </task>
    <task id="LOCAL-00002" summary="微调评估方式">
      <option name="closed" value="true" />
      <created>1753088813007</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753088813007</updated>
    </task>
    <task id="LOCAL-00003" summary="加入训练早停">
      <option name="closed" value="true" />
      <created>1753163811823</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753163811823</updated>
    </task>
    <task id="LOCAL-00004" summary="修复早停错误逻辑">
      <option name="closed" value="true" />
      <created>1753172935344</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753172935344</updated>
    </task>
    <task id="LOCAL-00005" summary="代码细节调整 负采样错误修正">
      <option name="closed" value="true" />
      <created>1753666873025</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753666873025</updated>
    </task>
    <task id="LOCAL-00006" summary="代码细节调整 负采样错误修正">
      <option name="closed" value="true" />
      <created>1753667934428</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753667934428</updated>
    </task>
    <task id="LOCAL-00007" summary="代码细节调整 负采样错误修正">
      <option name="closed" value="true" />
      <created>1753667967479</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753667967479</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="继续融合SAS.torch 用pre-ln改进性能" />
    <MESSAGE value="微调评估方式" />
    <MESSAGE value="加入训练早停" />
    <MESSAGE value="修复早停错误逻辑" />
    <MESSAGE value="代码细节调整 负采样错误修正" />
    <option name="LAST_COMMIT_MESSAGE" value="代码细节调整 负采样错误修正" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/FedSeqRecCodePro$test_norm_first.coverage" NAME="test_norm_first 覆盖结果" MODIFIED="1753083946325" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/FedSASRec/code" />
    <SUITE FILE_PATH="coverage/FedSeqRecCodePro$main.coverage" NAME="main 覆盖结果" MODIFIED="1754033565715" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/FedSASRec/code" />
  </component>
</project>