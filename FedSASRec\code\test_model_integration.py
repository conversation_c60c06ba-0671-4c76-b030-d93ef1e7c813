"""
测试模型集成脚本
验证FMLP模型能够正确集成到FedRec项目中
"""

import sys
import torch
import numpy as np
from parse import parse_args
from model_factory import create_model, get_model_info, validate_config


def test_model_creation():
    """测试模型创建功能"""
    print("=" * 50)
    print("测试模型创建功能")
    print("=" * 50)
    
    # 测试SASRec模型
    print("\n1. 测试SASRec模型创建:")
    try:
        # 模拟SASRec配置
        sasrec_config = {
            'model': 'SASRec',
            'embed_dim': 64,
            'num_layers': 2,
            'num_heads': 2,
            'dropout': 0.2,
            'max_seq_len': 50,
            'inner_size': 256
        }
        
        # 验证配置
        is_valid, error_msg = validate_config(sasrec_config)
        if not is_valid:
            print(f"   ❌ SASRec配置验证失败: {error_msg}")
            return False
        
        # 创建模型
        sasrec_model = create_model(sasrec_config, item_maxid=1000)
        print(f"   ✅ SASRec模型创建成功: {type(sasrec_model).__name__}")
        
        # 获取模型信息
        model_info = get_model_info(sasrec_config)
        print(f"   📋 模型信息: {model_info['model_name']} - {model_info['description']}")
        
    except Exception as e:
        print(f"   ❌ SASRec模型创建失败: {str(e)}")
        return False
    
    # 测试FMLP模型
    print("\n2. 测试FMLP模型创建:")
    try:
        # 模拟FMLP配置
        fmlp_config = {
            'model': 'FMLP',
            'item_embedding_dim': 64,
            'num_layers': 2,
            'dropout_prob': 0.5,
            'max_seq_len': 50,
            'learning_rate': 0.001,
            'weight_decay': 0.0
        }
        
        # 验证配置
        is_valid, error_msg = validate_config(fmlp_config)
        if not is_valid:
            print(f"   ❌ FMLP配置验证失败: {error_msg}")
            return False
        
        # 创建模型
        fmlp_model = create_model(fmlp_config, item_maxid=1000)
        print(f"   ✅ FMLP模型创建成功: {type(fmlp_model).__name__}")
        
        # 获取模型信息
        model_info = get_model_info(fmlp_config)
        print(f"   📋 模型信息: {model_info['model_name']} - {model_info['description']}")
        
    except Exception as e:
        print(f"   ❌ FMLP模型创建失败: {str(e)}")
        return False
    
    return True


def test_model_forward():
    """测试模型前向传播"""
    print("\n" + "=" * 50)
    print("测试模型前向传播")
    print("=" * 50)
    
    # 创建测试数据
    batch_size = 4
    seq_len = 10
    item_maxid = 100
    
    # 生成随机序列数据
    item_seq = torch.randint(1, item_maxid, (batch_size, seq_len))
    seq_lens = torch.randint(5, seq_len, (batch_size,))
    
    print(f"\n测试数据: batch_size={batch_size}, seq_len={seq_len}, item_maxid={item_maxid}")
    
    # 测试SASRec前向传播
    print("\n1. 测试SASRec前向传播:")
    try:
        sasrec_config = {
            'model': 'SASRec',
            'embed_dim': 32,
            'num_layers': 1,
            'num_heads': 1,
            'dropout': 0.1,
            'max_seq_len': seq_len,
            'inner_size': 64
        }
        
        sasrec_model = create_model(sasrec_config, item_maxid)
        sasrec_model.eval()
        
        with torch.no_grad():
            output = sasrec_model(item_seq, seq_lens)
            print(f"   ✅ SASRec输出形状: {output.shape}")
            print(f"   📊 期望形状: ({batch_size}, {seq_len}, {item_maxid + 1})")
            
    except Exception as e:
        print(f"   ❌ SASRec前向传播失败: {str(e)}")
        return False
    
    # 测试FMLP前向传播
    print("\n2. 测试FMLP前向传播:")
    try:
        fmlp_config = {
            'model': 'FMLP',
            'item_embedding_dim': 32,
            'num_layers': 1,
            'dropout_prob': 0.1,
            'max_seq_len': seq_len
        }
        
        fmlp_model = create_model(fmlp_config, item_maxid)
        fmlp_model.eval()
        
        with torch.no_grad():
            output = fmlp_model(item_seq, seq_lens)
            print(f"   ✅ FMLP输出形状: {output.shape}")
            print(f"   📊 期望形状: ({batch_size}, {seq_len}, {item_maxid + 1})")
            
    except Exception as e:
        print(f"   ❌ FMLP前向传播失败: {str(e)}")
        return False
    
    return True


def test_parse_args():
    """测试参数解析功能"""
    print("\n" + "=" * 50)
    print("测试参数解析功能")
    print("=" * 50)
    
    # 保存原始命令行参数
    original_argv = sys.argv.copy()
    
    try:
        # 测试SASRec参数解析
        print("\n1. 测试SASRec参数解析:")
        sys.argv = ['test_script.py', '--model', 'SASRec', '--embed_dim', '64', '--num_layers', '2']
        config = parse_args()
        print(f"   ✅ 解析成功，模型类型: {config['model']}")
        print(f"   📋 关键参数: embed_dim={config['embed_dim']}, num_layers={config['num_layers']}")
        
        # 测试FMLP参数解析
        print("\n2. 测试FMLP参数解析:")
        sys.argv = ['test_script.py', '--model', 'FMLP', '--item_embedding_dim', '64', '--num_layers', '2']
        config = parse_args()
        print(f"   ✅ 解析成功，模型类型: {config['model']}")
        print(f"   📋 关键参数: item_embedding_dim={config['item_embedding_dim']}, num_layers={config['num_layers']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 参数解析失败: {str(e)}")
        return False
        
    finally:
        # 恢复原始命令行参数
        sys.argv = original_argv


def main():
    """主测试函数"""
    print("🚀 开始测试FMLP模型集成")
    
    success_count = 0
    total_tests = 3
    
    # 运行测试
    if test_model_creation():
        success_count += 1
    
    if test_model_forward():
        success_count += 1
    
    if test_parse_args():
        success_count += 1
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {success_count}")
    print(f"失败测试: {total_tests - success_count}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！FMLP模型集成成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
